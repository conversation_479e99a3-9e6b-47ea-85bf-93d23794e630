// Package models defines the data structures used throughout the application.
// This demonstrates <PERSON>'s struct types and JSON marshaling/unmarshaling.
package models

import (
	"errors"
	"time"
)

// Book represents a book in our library system.
// This struct demonstrates Go's struct types, JSON tags, and field validation.
type Book struct {
	// ID is the unique identifier for the book
	ID string `json:"id"`
	
	// Title is the book's title (required field)
	Title string `json:"title" validate:"required"`
	
	// Author is the book's author (required field)
	Author string `json:"author" validate:"required"`
	
	// ISBN is the International Standard Book Number (optional but should be unique if provided)
	ISBN string `json:"isbn,omitempty"`
	
	// PublishedYear is the year the book was published
	PublishedYear int `json:"published_year,omitempty"`
	
	// Genre represents the book's category
	Genre string `json:"genre,omitempty"`
	
	// Description provides a brief summary of the book
	Description string `json:"description,omitempty"`
	
	// Available indicates whether the book is currently available for checkout
	Available bool `json:"available"`
	
	// CreatedAt tracks when the book record was created
	CreatedAt time.Time `json:"created_at"`
	
	// UpdatedAt tracks when the book record was last modified
	UpdatedAt time.Time `json:"updated_at"`
}

// BookCreateRequest represents the data needed to create a new book.
// This separates input validation from the internal Book model.
type BookCreateRequest struct {
	Title         string `json:"title" validate:"required"`
	Author        string `json:"author" validate:"required"`
	ISBN          string `json:"isbn,omitempty"`
	PublishedYear int    `json:"published_year,omitempty"`
	Genre         string `json:"genre,omitempty"`
	Description   string `json:"description,omitempty"`
}

// BookUpdateRequest represents the data that can be updated for an existing book.
// Using pointers allows us to distinguish between zero values and unset fields.
type BookUpdateRequest struct {
	Title         *string `json:"title,omitempty"`
	Author        *string `json:"author,omitempty"`
	ISBN          *string `json:"isbn,omitempty"`
	PublishedYear *int    `json:"published_year,omitempty"`
	Genre         *string `json:"genre,omitempty"`
	Description   *string `json:"description,omitempty"`
	Available     *bool   `json:"available,omitempty"`
}

// Validate checks if the BookCreateRequest has all required fields.
// This demonstrates Go's error handling patterns.
func (req *BookCreateRequest) Validate() error {
	if req.Title == "" {
		return errors.New("title is required")
	}
	if req.Author == "" {
		return errors.New("author is required")
	}
	if req.PublishedYear < 0 || req.PublishedYear > time.Now().Year()+1 {
		return errors.New("published year must be valid")
	}
	return nil
}

// ToBook converts a BookCreateRequest to a Book model.
// This demonstrates Go's method receivers and struct composition.
func (req *BookCreateRequest) ToBook(id string) *Book {
	now := time.Now()
	return &Book{
		ID:            id,
		Title:         req.Title,
		Author:        req.Author,
		ISBN:          req.ISBN,
		PublishedYear: req.PublishedYear,
		Genre:         req.Genre,
		Description:   req.Description,
		Available:     true, // New books are available by default
		CreatedAt:     now,
		UpdatedAt:     now,
	}
}

// ApplyUpdate applies the changes from BookUpdateRequest to the Book.
// This demonstrates pointer usage and conditional updates.
func (b *Book) ApplyUpdate(req *BookUpdateRequest) {
	if req.Title != nil {
		b.Title = *req.Title
	}
	if req.Author != nil {
		b.Author = *req.Author
	}
	if req.ISBN != nil {
		b.ISBN = *req.ISBN
	}
	if req.PublishedYear != nil {
		b.PublishedYear = *req.PublishedYear
	}
	if req.Genre != nil {
		b.Genre = *req.Genre
	}
	if req.Description != nil {
		b.Description = *req.Description
	}
	if req.Available != nil {
		b.Available = *req.Available
	}
	
	// Always update the timestamp when any field changes
	b.UpdatedAt = time.Now()
}
