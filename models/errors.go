// Package models defines custom error types and response structures.
// This demonstrates Go's error handling patterns and custom error types.
package models

import (
	"fmt"
	"net/http"
)

// Custom error types that implement the error interface.
// This shows how to create meaningful, typed errors in Go.

// NotFoundError represents a resource not found error.
type NotFoundError struct {
	Resource string
	ID       string
}

func (e *NotFoundError) Error() string {
	return fmt.Sprintf("%s with ID '%s' not found", e.Resource, e.ID)
}

// ValidationError represents input validation errors.
type ValidationError struct {
	Field   string
	Message string
}

func (e *ValidationError) Error() string {
	return fmt.Sprintf("validation error for field '%s': %s", e.Field, e.Message)
}

// ConflictError represents a resource conflict error (e.g., duplicate ISBN).
type ConflictError struct {
	Resource string
	Field    string
	Value    string
}

func (e *ConflictError) Error() string {
	return fmt.Sprintf("%s with %s '%s' already exists", e.<PERSON>, e.Field, e.Value)
}

// APIResponse represents a standard API response structure.
// This demonstrates consistent response formatting across the API.
type APIResponse struct {
	Success bool        `json:"success"`
	Data    interface{} `json:"data,omitempty"`
	Error   string      `json:"error,omitempty"`
	Message string      `json:"message,omitempty"`
}

// ErrorResponse creates a standardized error response.
func ErrorResponse(message string) *APIResponse {
	return &APIResponse{
		Success: false,
		Error:   message,
	}
}

// SuccessResponse creates a standardized success response.
func SuccessResponse(data interface{}, message string) *APIResponse {
	return &APIResponse{
		Success: true,
		Data:    data,
		Message: message,
	}
}

// GetHTTPStatusCode returns the appropriate HTTP status code for different error types.
// This demonstrates type assertion and error handling patterns.
func GetHTTPStatusCode(err error) int {
	switch err.(type) {
	case *NotFoundError:
		return http.StatusNotFound
	case *ValidationError:
		return http.StatusBadRequest
	case *ConflictError:
		return http.StatusConflict
	default:
		return http.StatusInternalServerError
	}
}
