// Package models contains unit tests for the book model.
// This demonstrates Go testing patterns and best practices.
package models

import (
	"testing"
	"time"
)

// TestBookCreateRequest_Validate tests the validation logic for book creation requests.
// This demonstrates table-driven tests, a common Go testing pattern.
func TestBookCreateRequest_Validate(t *testing.T) {
	tests := []struct {
		name    string
		req     BookCreateRequest
		wantErr bool
		errMsg  string
	}{
		{
			name: "valid book request",
			req: BookCreateRequest{
				Title:         "Test Book",
				Author:        "Test Author",
				ISBN:          "978-1234567890",
				PublishedYear: 2020,
				Genre:         "fiction",
				Description:   "A test book description",
			},
			wantErr: false,
		},
		{
			name: "missing title",
			req: BookCreateRequest{
				Title:  "",
				Author: "Test Author",
			},
			wantErr: true,
			errMsg:  "title is required",
		},
		{
			name: "missing author",
			req: BookCreateRequest{
				Title:  "Test Book",
				Author: "",
			},
			wantErr: true,
			errMsg:  "author is required",
		},
		{
			name: "invalid published year - too old",
			req: BookCreateRequest{
				Title:         "Test Book",
				Author:        "Test Author",
				PublishedYear: 1000,
			},
			wantErr: true,
			errMsg:  "published year must be valid",
		},
		{
			name: "invalid published year - future",
			req: BookCreateRequest{
				Title:         "Test Book",
				Author:        "Test Author",
				PublishedYear: time.Now().Year() + 5,
			},
			wantErr: true,
			errMsg:  "published year must be valid",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := tt.req.Validate()
			
			if tt.wantErr {
				if err == nil {
					t.Errorf("BookCreateRequest.Validate() expected error but got none")
					return
				}
				if tt.errMsg != "" && err.Error() != tt.errMsg {
					t.Errorf("BookCreateRequest.Validate() error = %v, want %v", err.Error(), tt.errMsg)
				}
			} else {
				if err != nil {
					t.Errorf("BookCreateRequest.Validate() unexpected error = %v", err)
				}
			}
		})
	}
}

// TestBookCreateRequest_ToBook tests the conversion from request to book model.
// This demonstrates testing struct methods and field assignments.
func TestBookCreateRequest_ToBook(t *testing.T) {
	req := &BookCreateRequest{
		Title:         "Test Book",
		Author:        "Test Author",
		ISBN:          "978-1234567890",
		PublishedYear: 2020,
		Genre:         "fiction",
		Description:   "A test book description",
	}

	id := "test-id-123"
	book := req.ToBook(id)

	// Test that all fields are correctly assigned
	if book.ID != id {
		t.Errorf("ToBook() ID = %v, want %v", book.ID, id)
	}
	if book.Title != req.Title {
		t.Errorf("ToBook() Title = %v, want %v", book.Title, req.Title)
	}
	if book.Author != req.Author {
		t.Errorf("ToBook() Author = %v, want %v", book.Author, req.Author)
	}
	if book.ISBN != req.ISBN {
		t.Errorf("ToBook() ISBN = %v, want %v", book.ISBN, req.ISBN)
	}
	if book.PublishedYear != req.PublishedYear {
		t.Errorf("ToBook() PublishedYear = %v, want %v", book.PublishedYear, req.PublishedYear)
	}
	if book.Genre != req.Genre {
		t.Errorf("ToBook() Genre = %v, want %v", book.Genre, req.Genre)
	}
	if book.Description != req.Description {
		t.Errorf("ToBook() Description = %v, want %v", book.Description, req.Description)
	}

	// Test default values
	if !book.Available {
		t.Errorf("ToBook() Available = %v, want %v", book.Available, true)
	}
	if book.CreatedAt.IsZero() {
		t.Errorf("ToBook() CreatedAt should not be zero")
	}
	if book.UpdatedAt.IsZero() {
		t.Errorf("ToBook() UpdatedAt should not be zero")
	}
}

// TestBook_ApplyUpdate tests the update logic for books.
// This demonstrates testing pointer fields and partial updates.
func TestBook_ApplyUpdate(t *testing.T) {
	// Create a base book
	book := &Book{
		ID:            "test-id",
		Title:         "Original Title",
		Author:        "Original Author",
		ISBN:          "978-1111111111",
		PublishedYear: 2020,
		Genre:         "fiction",
		Description:   "Original description",
		Available:     true,
		CreatedAt:     time.Now().Add(-time.Hour),
		UpdatedAt:     time.Now().Add(-time.Hour),
	}

	originalUpdatedAt := book.UpdatedAt

	// Create update request with some fields
	newTitle := "Updated Title"
	newAvailable := false
	updateReq := &BookUpdateRequest{
		Title:     &newTitle,
		Available: &newAvailable,
		// Other fields are nil, so they shouldn't be updated
	}

	// Apply the update
	book.ApplyUpdate(updateReq)

	// Test that updated fields changed
	if book.Title != newTitle {
		t.Errorf("ApplyUpdate() Title = %v, want %v", book.Title, newTitle)
	}
	if book.Available != newAvailable {
		t.Errorf("ApplyUpdate() Available = %v, want %v", book.Available, newAvailable)
	}

	// Test that non-updated fields remained the same
	if book.Author != "Original Author" {
		t.Errorf("ApplyUpdate() Author should not have changed, got %v", book.Author)
	}
	if book.ISBN != "978-1111111111" {
		t.Errorf("ApplyUpdate() ISBN should not have changed, got %v", book.ISBN)
	}

	// Test that UpdatedAt was changed
	if !book.UpdatedAt.After(originalUpdatedAt) {
		t.Errorf("ApplyUpdate() should have updated UpdatedAt timestamp")
	}

	// Test that CreatedAt was not changed
	if book.CreatedAt.After(originalUpdatedAt) {
		t.Errorf("ApplyUpdate() should not have changed CreatedAt timestamp")
	}
}

// TestCustomErrors tests our custom error types.
// This demonstrates testing error types and type assertions.
func TestCustomErrors(t *testing.T) {
	t.Run("NotFoundError", func(t *testing.T) {
		err := &NotFoundError{
			Resource: "Book",
			ID:       "test-id",
		}
		
		expected := "Book with ID 'test-id' not found"
		if err.Error() != expected {
			t.Errorf("NotFoundError.Error() = %v, want %v", err.Error(), expected)
		}
	})

	t.Run("ValidationError", func(t *testing.T) {
		err := &ValidationError{
			Field:   "title",
			Message: "field is required",
		}
		
		expected := "validation error for field 'title': field is required"
		if err.Error() != expected {
			t.Errorf("ValidationError.Error() = %v, want %v", err.Error(), expected)
		}
	})

	t.Run("ConflictError", func(t *testing.T) {
		err := &ConflictError{
			Resource: "Book",
			Field:    "ISBN",
			Value:    "978-1234567890",
		}
		
		expected := "Book with ISBN '978-1234567890' already exists"
		if err.Error() != expected {
			t.Errorf("ConflictError.Error() = %v, want %v", err.Error(), expected)
		}
	})
}

// BenchmarkBookCreateRequest_Validate benchmarks the validation function.
// This demonstrates Go's benchmarking capabilities.
func BenchmarkBookCreateRequest_Validate(b *testing.B) {
	req := &BookCreateRequest{
		Title:         "Test Book",
		Author:        "Test Author",
		ISBN:          "978-1234567890",
		PublishedYear: 2020,
		Genre:         "fiction",
		Description:   "A test book description",
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_ = req.Validate()
	}
}

// ExampleBookCreateRequest_ToBook demonstrates how to use the ToBook method.
// This is an example function that appears in Go documentation.
func ExampleBookCreateRequest_ToBook() {
	req := &BookCreateRequest{
		Title:         "Learning Go",
		Author:        "Jon Bodner",
		ISBN:          "978-1492077213",
		PublishedYear: 2021,
		Genre:         "technology",
		Description:   "An idiomatic approach to real-world Go programming",
	}

	book := req.ToBook("generated-id-123")
	
	// The book will have all the request fields plus generated fields
	_ = book.ID          // "generated-id-123"
	_ = book.Available   // true (default)
	_ = book.CreatedAt   // current timestamp
	_ = book.UpdatedAt   // current timestamp
}
