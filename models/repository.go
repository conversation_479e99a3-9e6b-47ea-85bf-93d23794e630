// Package models also contains interfaces that define contracts for data operations.
// This demonstrates Go's interface types and dependency injection patterns.
package models

// BookRepository defines the interface for book data operations.
// This is a key Go concept - interfaces define behavior, not implementation.
// Any type that implements these methods satisfies this interface.
type BookRepository interface {
	// Create adds a new book to the storage and returns the created book or an error.
	Create(book *Book) (*Book, error)
	
	// GetByID retrieves a book by its unique identifier.
	// Returns the book if found, or an error if not found or other issues occur.
	GetByID(id string) (*Book, error)
	
	// GetAll retrieves all books from storage.
	// Returns a slice of books and any error that occurred.
	GetAll() ([]*Book, error)
	
	// Update modifies an existing book in storage.
	// Returns the updated book or an error if the book doesn't exist or update fails.
	Update(id string, book *Book) (*Book, error)
	
	// Delete removes a book from storage by its ID.
	// Returns an error if the book doesn't exist or deletion fails.
	Delete(id string) error
	
	// GetByISBN retrieves a book by its ISBN.
	// This demonstrates additional query methods beyond basic CRUD.
	GetByISBN(isbn string) (*Book, error)
	
	// Search finds books that match the given criteria.
	// This shows how interfaces can include more complex operations.
	Search(query string) ([]*Book, error)
}

// BookService defines the business logic interface for book operations.
// This separates business logic from data access, demonstrating layered architecture.
type BookService interface {
	// CreateBook handles the business logic for creating a new book.
	CreateBook(req *BookCreateRequest) (*Book, error)
	
	// GetBook retrieves a book by ID with any necessary business logic.
	GetBook(id string) (*Book, error)
	
	// GetAllBooks retrieves all books with any filtering or sorting logic.
	GetAllBooks() ([]*Book, error)
	
	// UpdateBook handles the business logic for updating an existing book.
	UpdateBook(id string, req *BookUpdateRequest) (*Book, error)
	
	// DeleteBook handles the business logic for deleting a book.
	DeleteBook(id string) error
	
	// SearchBooks performs a search with business logic applied.
	SearchBooks(query string) ([]*Book, error)
	
	// CheckoutBook marks a book as unavailable (business logic example).
	CheckoutBook(id string) (*Book, error)
	
	// ReturnBook marks a book as available again (business logic example).
	ReturnBook(id string) (*Book, error)
}
