// Package main is the entry point for the Book Library API application.
// This demonstrates Go application structure, dependency injection, and HTTP server setup.
package main

import (
	"context"
	"fmt"
	"log"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"book-library-api/handlers"
	"book-library-api/models"
	"book-library-api/storage"
)

// Config holds the application configuration.
// This demonstrates configuration management patterns in Go.
type Config struct {
	Port         string
	ReadTimeout  time.Duration
	WriteTimeout time.Duration
	IdleTimeout  time.Duration
}

// getConfig returns the application configuration.
// This demonstrates environment variable usage and default values.
func getConfig() *Config {
	port := os.Getenv("PORT")
	if port == "" {
		port = "8080" // Default port
	}

	return &Config{
		Port:         port,
		ReadTimeout:  15 * time.Second,
		WriteTimeout: 15 * time.Second,
		IdleTimeout:  60 * time.Second,
	}
}

// initializeApp sets up all application dependencies.
// This demonstrates dependency injection and application initialization patterns.
func initializeApp() (models.BookService, error) {
	// Initialize the repository (data layer)
	repo := storage.NewMemoryBookRepository()

	// Initialize the service (business logic layer)
	service := handlers.NewBookService(repo)

	// Add some sample data for demonstration
	if err := addSampleData(service); err != nil {
		return nil, fmt.Errorf("failed to add sample data: %w", err)
	}

	return service, nil
}

// addSampleData adds some initial books to the library for demonstration.
// This demonstrates data seeding and service usage.
func addSampleData(service models.BookService) error {
	sampleBooks := []*models.BookCreateRequest{
		{
			Title:         "The Go Programming Language",
			Author:        "Alan Donovan and Brian Kernighan",
			ISBN:          "978-0134190440",
			PublishedYear: 2015,
			Genre:         "technology",
			Description:   "The authoritative resource to writing clear and idiomatic Go code.",
		},
		{
			Title:         "Clean Code",
			Author:        "Robert C. Martin",
			ISBN:          "978-0132350884",
			PublishedYear: 2008,
			Genre:         "technology",
			Description:   "A handbook of agile software craftsmanship with practical advice for writing clean, maintainable code.",
		},
		{
			Title:         "The Pragmatic Programmer",
			Author:        "David Thomas and Andrew Hunt",
			ISBN:          "978-0201616224",
			PublishedYear: 1999,
			Genre:         "technology",
			Description:   "Your journey to mastery in software development with practical tips and techniques.",
		},
		{
			Title:         "Design Patterns",
			Author:        "Gang of Four",
			ISBN:          "978-0201633610",
			PublishedYear: 1994,
			Genre:         "technology",
			Description:   "Elements of reusable object-oriented software design patterns.",
		},
		{
			Title:         "Effective Go",
			Author:        "The Go Team",
			PublishedYear: 2020,
			Genre:         "technology",
			Description:   "Best practices and idioms for writing effective Go code.",
		},
	}

	for _, bookReq := range sampleBooks {
		_, err := service.CreateBook(bookReq)
		if err != nil {
			return fmt.Errorf("failed to create sample book '%s': %w", bookReq.Title, err)
		}
	}

	log.Printf("Added %d sample books to the library", len(sampleBooks))
	return nil
}

// createServer creates and configures the HTTP server.
// This demonstrates HTTP server configuration and best practices.
func createServer(config *Config, service models.BookService) *http.Server {
	// Set up the router with all endpoints
	router := handlers.SetupRouter(service)

	// Create the HTTP server with timeouts
	server := &http.Server{
		Addr:         ":" + config.Port,
		Handler:      router,
		ReadTimeout:  config.ReadTimeout,
		WriteTimeout: config.WriteTimeout,
		IdleTimeout:  config.IdleTimeout,
	}

	return server
}

// gracefulShutdown handles graceful server shutdown.
// This demonstrates proper resource cleanup and signal handling.
func gracefulShutdown(server *http.Server) {
	// Create a channel to receive OS signals
	quit := make(chan os.Signal, 1)
	
	// Register the channel to receive specific signals
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	
	// Block until a signal is received
	<-quit
	log.Println("Shutting down server...")

	// Create a context with timeout for shutdown
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// Attempt graceful shutdown
	if err := server.Shutdown(ctx); err != nil {
		log.Printf("Server forced to shutdown: %v", err)
	}

	log.Println("Server exited")
}

// main is the application entry point.
// This demonstrates the main function pattern and error handling.
func main() {
	// Print startup banner
	fmt.Println("🚀 Starting Book Library API...")
	fmt.Println("📚 A learning-focused REST API built with Go")
	fmt.Println()

	// Get configuration
	config := getConfig()

	// Initialize application dependencies
	service, err := initializeApp()
	if err != nil {
		log.Fatalf("Failed to initialize application: %v", err)
	}

	// Create and configure the HTTP server
	server := createServer(config, service)

	// Start the server in a goroutine
	go func() {
		log.Printf("🌐 Server starting on port %s", config.Port)
		log.Printf("📖 API Documentation: http://localhost:%s/", config.Port)
		log.Printf("🏥 Health Check: http://localhost:%s/api/v1/health", config.Port)
		log.Printf("📚 Books Endpoint: http://localhost:%s/api/v1/books", config.Port)
		log.Println()
		log.Println("Press Ctrl+C to stop the server")

		if err := server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			log.Fatalf("Failed to start server: %v", err)
		}
	}()

	// Wait for interrupt signal to gracefully shutdown the server
	gracefulShutdown(server)
}
