#!/bin/bash

# Book Library API - Test Script
# This script demonstrates how to test all API endpoints
# Make sure the server is running on localhost:8080 before executing

BASE_URL="http://localhost:8080/api/v1"
BOOK_ID=""

echo "🚀 Testing Book Library API"
echo "=========================="
echo

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print test headers
print_test() {
    echo -e "${BLUE}📋 Testing: $1${NC}"
    echo "----------------------------------------"
}

# Function to print success
print_success() {
    echo -e "${GREEN}✅ $1${NC}"
    echo
}

# Function to print error
print_error() {
    echo -e "${RED}❌ $1${NC}"
    echo
}

# Test 1: Health Check
print_test "Health Check"
response=$(curl -s -w "%{http_code}" "$BASE_URL/health")
http_code="${response: -3}"
if [ "$http_code" = "200" ]; then
    print_success "Health check passed"
else
    print_error "Health check failed (HTTP $http_code)"
fi

# Test 2: Get All Books (should include sample data)
print_test "Get All Books"
response=$(curl -s -w "%{http_code}" "$BASE_URL/books")
http_code="${response: -3}"
if [ "$http_code" = "200" ]; then
    print_success "Retrieved all books successfully"
    echo "Sample response:"
    echo "${response%???}" | jq '.' 2>/dev/null || echo "${response%???}"
    echo
else
    print_error "Failed to get books (HTTP $http_code)"
fi

# Test 3: Create a New Book
print_test "Create New Book"
create_response=$(curl -s -w "%{http_code}" -X POST "$BASE_URL/books" \
    -H "Content-Type: application/json" \
    -d '{
        "title": "Test Book for API Demo",
        "author": "API Test Author",
        "isbn": "978-**********",
        "published_year": 2024,
        "genre": "technology",
        "description": "A test book created to demonstrate the API functionality"
    }')

http_code="${create_response: -3}"
if [ "$http_code" = "201" ]; then
    print_success "Book created successfully"
    # Extract book ID for further tests
    BOOK_ID=$(echo "${create_response%???}" | jq -r '.data.id' 2>/dev/null)
    echo "Created book ID: $BOOK_ID"
    echo "Response:"
    echo "${create_response%???}" | jq '.' 2>/dev/null || echo "${create_response%???}"
    echo
else
    print_error "Failed to create book (HTTP $http_code)"
    echo "${create_response%???}"
    echo
fi

# Test 4: Get Specific Book (if we have an ID)
if [ ! -z "$BOOK_ID" ] && [ "$BOOK_ID" != "null" ]; then
    print_test "Get Specific Book"
    response=$(curl -s -w "%{http_code}" "$BASE_URL/books/$BOOK_ID")
    http_code="${response: -3}"
    if [ "$http_code" = "200" ]; then
        print_success "Retrieved specific book successfully"
        echo "Response:"
        echo "${response%???}" | jq '.' 2>/dev/null || echo "${response%???}"
        echo
    else
        print_error "Failed to get specific book (HTTP $http_code)"
    fi
fi

# Test 5: Search Books
print_test "Search Books"
response=$(curl -s -w "%{http_code}" "$BASE_URL/books?search=go")
http_code="${response: -3}"
if [ "$http_code" = "200" ]; then
    print_success "Search completed successfully"
    echo "Search results for 'go':"
    echo "${response%???}" | jq '.' 2>/dev/null || echo "${response%???}"
    echo
else
    print_error "Search failed (HTTP $http_code)"
fi

# Test 6: Update Book (if we have an ID)
if [ ! -z "$BOOK_ID" ] && [ "$BOOK_ID" != "null" ]; then
    print_test "Update Book"
    response=$(curl -s -w "%{http_code}" -X PUT "$BASE_URL/books/$BOOK_ID" \
        -H "Content-Type: application/json" \
        -d '{
            "title": "Updated Test Book Title",
            "description": "This book has been updated to demonstrate the PUT endpoint"
        }')
    
    http_code="${response: -3}"
    if [ "$http_code" = "200" ]; then
        print_success "Book updated successfully"
        echo "Response:"
        echo "${response%???}" | jq '.' 2>/dev/null || echo "${response%???}"
        echo
    else
        print_error "Failed to update book (HTTP $http_code)"
    fi
fi

# Test 7: Checkout Book (if we have an ID)
if [ ! -z "$BOOK_ID" ] && [ "$BOOK_ID" != "null" ]; then
    print_test "Checkout Book"
    response=$(curl -s -w "%{http_code}" -X POST "$BASE_URL/books/$BOOK_ID/checkout")
    http_code="${response: -3}"
    if [ "$http_code" = "200" ]; then
        print_success "Book checked out successfully"
        echo "Response:"
        echo "${response%???}" | jq '.' 2>/dev/null || echo "${response%???}"
        echo
    else
        print_error "Failed to checkout book (HTTP $http_code)"
    fi
fi

# Test 8: Return Book (if we have an ID)
if [ ! -z "$BOOK_ID" ] && [ "$BOOK_ID" != "null" ]; then
    print_test "Return Book"
    response=$(curl -s -w "%{http_code}" -X POST "$BASE_URL/books/$BOOK_ID/return")
    http_code="${response: -3}"
    if [ "$http_code" = "200" ]; then
        print_success "Book returned successfully"
        echo "Response:"
        echo "${response%???}" | jq '.' 2>/dev/null || echo "${response%???}"
        echo
    else
        print_error "Failed to return book (HTTP $http_code)"
    fi
fi

# Test 9: Error Handling - Try to get non-existent book
print_test "Error Handling (Non-existent Book)"
response=$(curl -s -w "%{http_code}" "$BASE_URL/books/nonexistent-id")
http_code="${response: -3}"
if [ "$http_code" = "404" ]; then
    print_success "Error handling works correctly (404 for non-existent book)"
    echo "Error response:"
    echo "${response%???}" | jq '.' 2>/dev/null || echo "${response%???}"
    echo
else
    print_error "Unexpected response for non-existent book (HTTP $http_code)"
fi

# Test 10: Validation Error - Try to create book with missing required fields
print_test "Validation Error (Missing Required Fields)"
response=$(curl -s -w "%{http_code}" -X POST "$BASE_URL/books" \
    -H "Content-Type: application/json" \
    -d '{"title": ""}')

http_code="${response: -3}"
if [ "$http_code" = "400" ]; then
    print_success "Validation error handling works correctly"
    echo "Validation error response:"
    echo "${response%???}" | jq '.' 2>/dev/null || echo "${response%???}"
    echo
else
    print_error "Unexpected response for validation error (HTTP $http_code)"
fi

# Clean up - Delete the test book (if we have an ID)
if [ ! -z "$BOOK_ID" ] && [ "$BOOK_ID" != "null" ]; then
    print_test "Delete Test Book (Cleanup)"
    response=$(curl -s -w "%{http_code}" -X DELETE "$BASE_URL/books/$BOOK_ID")
    http_code="${response: -3}"
    if [ "$http_code" = "204" ]; then
        print_success "Test book deleted successfully"
    else
        print_error "Failed to delete test book (HTTP $http_code)"
    fi
fi

echo -e "${GREEN}🎉 API Testing Complete!${NC}"
echo
echo "💡 Tips for further testing:"
echo "  - Try different search queries"
echo "  - Test with invalid JSON data"
echo "  - Test with very long strings"
echo "  - Try concurrent requests"
echo "  - Monitor server logs while testing"
