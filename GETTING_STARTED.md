# 🚀 Getting Started with Book Library API

Welcome to your Go learning journey! This guide will help you get up and running with the Book Library API project in just a few minutes.

## ⚡ Quick Setup (5 minutes)

### Step 1: Verify Go Installation
```bash
go version
```
If you see a version number (1.21 or later), you're good to go! If not, install Go from [https://golang.org/dl/](https://golang.org/dl/).

### Step 2: Navigate to Project Directory
```bash
cd /path/to/book-library-api
```

### Step 3: Install Dependencies
```bash
go mod tidy
```

### Step 4: Run the Application
```bash
go run main.go
```

You should see:
```
🚀 Starting Book Library API...
📚 A learning-focused REST API built with Go

🌐 Server starting on port 8080
📖 API Documentation: http://localhost:8080/
```

### Step 5: Test It Works
Open a new terminal and run:
```bash
curl http://localhost:8080/api/v1/health
```

You should get a JSON response indicating the API is healthy!

## 🧪 Run the Complete Test Suite

We've included a comprehensive test script that demonstrates all API features:

```bash
./examples/test_requests.sh
```

This script will:
- ✅ Test all endpoints
- ✅ Show example requests and responses
- ✅ Demonstrate error handling
- ✅ Clean up test data

## 📚 What You'll Learn

### 1. **Go Fundamentals** (Start Here)
- **File**: `models/book.go`
- **Concepts**: Structs, JSON tags, methods
- **Try**: Modify the Book struct to add new fields

### 2. **Interfaces & Dependency Injection**
- **File**: `models/repository.go`
- **Concepts**: Interface definitions, contracts
- **Try**: Add a new method to the BookRepository interface

### 3. **HTTP Handling**
- **File**: `handlers/book_handlers.go`
- **Concepts**: HTTP methods, request/response handling
- **Try**: Add a new endpoint for book statistics

### 4. **Error Handling**
- **File**: `models/errors.go`
- **Concepts**: Custom error types, error wrapping
- **Try**: Create a new custom error type

### 5. **Concurrent Programming**
- **File**: `storage/memory.go`
- **Concepts**: Mutexes, thread-safe operations
- **Try**: Add logging to see concurrent access patterns

### 6. **Testing**
- **File**: `models/book_test.go`
- **Concepts**: Unit tests, table-driven tests, benchmarks
- **Try**: Run tests with `go test ./models/`

## 🎯 Learning Challenges

### Beginner Challenges
1. **Add a new field** to the Book struct (e.g., `Pages int`)
2. **Create a new endpoint** to get books by author
3. **Add validation** for the new field you created
4. **Write a test** for your new functionality

### Intermediate Challenges
1. **Implement pagination** for the GetAllBooks endpoint
2. **Add sorting** options (by title, author, year)
3. **Create a statistics endpoint** showing total books, available books, etc.
4. **Add request logging middleware**

### Advanced Challenges
1. **Replace in-memory storage** with file-based storage (JSON files)
2. **Add authentication** using JWT tokens
3. **Implement rate limiting** middleware
4. **Add database integration** (PostgreSQL or SQLite)

## 🔧 Development Tips

### Running Tests
```bash
# Run all tests
go test ./...

# Run tests with coverage
go test -cover ./...

# Run specific test
go test -run TestBookCreateRequest_Validate ./models/

# Run benchmarks
go test -bench=. ./models/
```

### Code Formatting
```bash
# Format all Go files
go fmt ./...

# Check for common issues
go vet ./...
```

### Building the Application
```bash
# Build for current platform
go build -o book-api main.go

# Build for different platforms
GOOS=linux GOARCH=amd64 go build -o book-api-linux main.go
GOOS=windows GOARCH=amd64 go build -o book-api.exe main.go
```

## 🐛 Troubleshooting

### Common Issues

**"command not found: go"**
- Install Go from [https://golang.org/dl/](https://golang.org/dl/)
- Make sure Go is in your PATH

**"port already in use"**
- Change the port: `PORT=3000 go run main.go`
- Or kill the process using port 8080

**"module not found"**
- Run `go mod tidy` to download dependencies
- Make sure you're in the project directory

**Tests failing**
- Make sure the server is not running when running tests
- Check that you haven't modified the test files

### Getting Help

1. **Read the error messages** - Go has excellent error messages
2. **Check the logs** - The server logs all requests
3. **Use the test script** - It shows expected behavior
4. **Experiment** - Try breaking things to understand how they work

## 📖 Next Steps

Once you're comfortable with the basics:

1. **Read the full README.md** for detailed explanations
2. **Explore each package** to understand the architecture
3. **Try the learning challenges** above
4. **Build your own features** - the best way to learn!

## 🎉 You're Ready!

You now have a fully functional REST API written in Go. Take your time exploring the code, running tests, and experimenting with new features. Remember:

- **Break things** - it's the best way to learn
- **Read the comments** - they explain Go concepts
- **Experiment** - try modifying the code
- **Have fun** - programming should be enjoyable!

Happy coding! 🚀
