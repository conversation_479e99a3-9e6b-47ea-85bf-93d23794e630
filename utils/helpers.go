// Package utils provides utility functions used throughout the application.
// This demonstrates Go's package organization and utility function patterns.
package utils

import (
	"crypto/rand"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"net/http"
	"strconv"
	"strings"

	"book-library-api/models"
)

// GenerateID creates a random unique identifier.
// This demonstrates Go's crypto/rand package and error handling.
func GenerateID() (string, error) {
	bytes := make([]byte, 8) // 8 bytes = 16 hex characters
	if _, err := rand.Read(bytes); err != nil {
		return "", fmt.Errorf("failed to generate ID: %w", err)
	}
	return hex.EncodeToString(bytes), nil
}

// WriteJSONResponse writes a JSON response to the HTTP response writer.
// This demonstrates JSON marshaling and HTTP response handling.
func WriteJSONResponse(w http.ResponseWriter, statusCode int, data interface{}) {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(statusCode)
	
	if err := json.NewEncoder(w).Encode(data); err != nil {
		// If we can't encode the response, write a simple error message
		http.Error(w, "Internal server error", http.StatusInternalServerError)
	}
}

// WriteErrorResponse writes a standardized error response.
// This demonstrates error response standardization and HTTP status codes.
func WriteErrorResponse(w http.ResponseWriter, err error) {
	statusCode := models.GetHTTPStatusCode(err)
	response := models.ErrorResponse(err.Error())
	WriteJSONResponse(w, statusCode, response)
}

// WriteSuccessResponse writes a standardized success response.
// This demonstrates success response standardization.
func WriteSuccessResponse(w http.ResponseWriter, data interface{}, message string) {
	response := models.SuccessResponse(data, message)
	WriteJSONResponse(w, http.StatusOK, response)
}

// ParseJSONBody parses JSON from the request body into the provided interface.
// This demonstrates JSON unmarshaling and error handling.
func ParseJSONBody(r *http.Request, v interface{}) error {
	if r.Body == nil {
		return fmt.Errorf("request body is empty")
	}
	
	decoder := json.NewDecoder(r.Body)
	decoder.DisallowUnknownFields() // Strict parsing - reject unknown fields
	
	if err := decoder.Decode(v); err != nil {
		return fmt.Errorf("invalid JSON: %w", err)
	}
	
	return nil
}

// GetQueryParam extracts a query parameter from the request.
// This demonstrates URL parameter handling.
func GetQueryParam(r *http.Request, key string) string {
	return r.URL.Query().Get(key)
}

// GetQueryParamInt extracts an integer query parameter from the request.
// This demonstrates type conversion and error handling.
func GetQueryParamInt(r *http.Request, key string) (int, error) {
	value := r.URL.Query().Get(key)
	if value == "" {
		return 0, fmt.Errorf("parameter '%s' is required", key)
	}
	
	intValue, err := strconv.Atoi(value)
	if err != nil {
		return 0, fmt.Errorf("parameter '%s' must be a valid integer", key)
	}
	
	return intValue, nil
}

// ValidateRequiredFields checks if required string fields are not empty.
// This demonstrates validation patterns and variadic functions.
func ValidateRequiredFields(fields map[string]string) error {
	for fieldName, fieldValue := range fields {
		if strings.TrimSpace(fieldValue) == "" {
			return &models.ValidationError{
				Field:   fieldName,
				Message: "field is required and cannot be empty",
			}
		}
	}
	return nil
}

// SanitizeString removes leading/trailing whitespace and converts to lowercase.
// This demonstrates string manipulation functions.
func SanitizeString(s string) string {
	return strings.ToLower(strings.TrimSpace(s))
}

// LogRequest logs basic information about HTTP requests.
// This demonstrates logging patterns (simplified for this example).
func LogRequest(r *http.Request) {
	fmt.Printf("[%s] %s %s\n", r.Method, r.URL.Path, r.RemoteAddr)
}
