// Package utils provides comprehensive validation functions.
// This demonstrates Go's validation patterns, regular expressions, and error handling.
package utils

import (
	"fmt"
	"regexp"
	"strconv"
	"strings"
	"time"

	"book-library-api/models"
)

// ISBN validation patterns
var (
	// ISBN-10 pattern: 10 digits with possible hyphens, last digit can be X
	isbn10Pattern = regexp.MustCompile(`^(?:\d{9}[\dX]|\d{1,5}-\d{1,7}-\d{1,7}-[\dX])$`)
	
	// ISBN-13 pattern: 13 digits with possible hyphens
	isbn13Pattern = regexp.MustCompile(`^(?:\d{13}|\d{1,5}-\d{1,7}-\d{1,7}-\d{1,7}-\d)$`)
)

// ValidateBookCreateRequest performs comprehensive validation on book creation requests.
// This demonstrates complex validation logic and multiple validation rules.
func ValidateBookCreateRequest(req *models.BookCreateRequest) error {
	// Validate required fields
	if err := validateRequiredString("title", req.Title); err != nil {
		return err
	}
	
	if err := validateRequiredString("author", req.Author); err != nil {
		return err
	}
	
	// Validate optional fields if provided
	if req.ISBN != "" {
		if err := validateISBN(req.ISBN); err != nil {
			return err
		}
	}
	
	if req.PublishedYear != 0 {
		if err := validatePublishedYear(req.PublishedYear); err != nil {
			return err
		}
	}
	
	if req.Genre != "" {
		if err := validateGenre(req.Genre); err != nil {
			return err
		}
	}
	
	if req.Description != "" {
		if err := validateDescription(req.Description); err != nil {
			return err
		}
	}
	
	return nil
}

// ValidateBookUpdateRequest performs validation on book update requests.
// This demonstrates validation for partial updates using pointers.
func ValidateBookUpdateRequest(req *models.BookUpdateRequest) error {
	// Validate fields only if they are provided (not nil)
	if req.Title != nil {
		if err := validateRequiredString("title", *req.Title); err != nil {
			return err
		}
	}
	
	if req.Author != nil {
		if err := validateRequiredString("author", *req.Author); err != nil {
			return err
		}
	}
	
	if req.ISBN != nil && *req.ISBN != "" {
		if err := validateISBN(*req.ISBN); err != nil {
			return err
		}
	}
	
	if req.PublishedYear != nil {
		if err := validatePublishedYear(*req.PublishedYear); err != nil {
			return err
		}
	}
	
	if req.Genre != nil && *req.Genre != "" {
		if err := validateGenre(*req.Genre); err != nil {
			return err
		}
	}
	
	if req.Description != nil && *req.Description != "" {
		if err := validateDescription(*req.Description); err != nil {
			return err
		}
	}
	
	return nil
}

// validateRequiredString checks if a required string field is not empty.
// This demonstrates basic string validation patterns.
func validateRequiredString(fieldName, value string) error {
	trimmed := strings.TrimSpace(value)
	if trimmed == "" {
		return &models.ValidationError{
			Field:   fieldName,
			Message: "field is required and cannot be empty",
		}
	}
	
	// Check minimum length
	if len(trimmed) < 2 {
		return &models.ValidationError{
			Field:   fieldName,
			Message: "field must be at least 2 characters long",
		}
	}
	
	// Check maximum length
	maxLength := getMaxLength(fieldName)
	if len(trimmed) > maxLength {
		return &models.ValidationError{
			Field:   fieldName,
			Message: fmt.Sprintf("field must not exceed %d characters", maxLength),
		}
	}
	
	return nil
}

// validateISBN validates ISBN format (both ISBN-10 and ISBN-13).
// This demonstrates regular expression usage and format validation.
func validateISBN(isbn string) error {
	// Remove spaces and hyphens for validation
	cleanISBN := strings.ReplaceAll(strings.ReplaceAll(isbn, "-", ""), " ", "")
	
	// Check if it matches ISBN-10 or ISBN-13 pattern
	if !isbn10Pattern.MatchString(cleanISBN) && !isbn13Pattern.MatchString(cleanISBN) {
		return &models.ValidationError{
			Field:   "isbn",
			Message: "invalid ISBN format (must be valid ISBN-10 or ISBN-13)",
		}
	}
	
	// Additional checksum validation could be added here
	return nil
}

// validatePublishedYear validates the publication year.
// This demonstrates date/year validation and business rule enforcement.
func validatePublishedYear(year int) error {
	currentYear := time.Now().Year()
	
	// Books can't be published before the printing press (1440) or in the future
	if year < 1440 {
		return &models.ValidationError{
			Field:   "published_year",
			Message: "published year cannot be before 1440",
		}
	}
	
	if year > currentYear+1 {
		return &models.ValidationError{
			Field:   "published_year",
			Message: fmt.Sprintf("published year cannot be later than %d", currentYear+1),
		}
	}
	
	return nil
}

// validateGenre validates the book genre.
// This demonstrates enumeration validation and case-insensitive matching.
func validateGenre(genre string) error {
	validGenres := []string{
		"fiction", "non-fiction", "mystery", "romance", "science-fiction",
		"fantasy", "thriller", "biography", "history", "self-help",
		"business", "technology", "health", "travel", "cooking",
		"art", "music", "sports", "religion", "philosophy",
	}
	
	genreLower := strings.ToLower(strings.TrimSpace(genre))
	
	for _, validGenre := range validGenres {
		if genreLower == validGenre {
			return nil
		}
	}
	
	return &models.ValidationError{
		Field:   "genre",
		Message: fmt.Sprintf("invalid genre '%s'. Valid genres: %s", genre, strings.Join(validGenres, ", ")),
	}
}

// validateDescription validates the book description.
// This demonstrates text length validation and content filtering.
func validateDescription(description string) error {
	trimmed := strings.TrimSpace(description)
	
	if len(trimmed) < 10 {
		return &models.ValidationError{
			Field:   "description",
			Message: "description must be at least 10 characters long",
		}
	}
	
	if len(trimmed) > 1000 {
		return &models.ValidationError{
			Field:   "description",
			Message: "description must not exceed 1000 characters",
		}
	}
	
	return nil
}

// getMaxLength returns the maximum allowed length for different fields.
// This demonstrates configuration-based validation.
func getMaxLength(fieldName string) int {
	maxLengths := map[string]int{
		"title":       200,
		"author":      100,
		"genre":       50,
		"description": 1000,
	}
	
	if length, exists := maxLengths[fieldName]; exists {
		return length
	}
	
	return 255 // default max length
}

// ValidateID validates that an ID is not empty and has a valid format.
// This demonstrates ID validation patterns.
func ValidateID(id string) error {
	if strings.TrimSpace(id) == "" {
		return &models.ValidationError{
			Field:   "id",
			Message: "ID is required",
		}
	}
	
	// Check if ID is a valid hexadecimal string (our generated IDs are hex)
	if _, err := strconv.ParseUint(id, 16, 64); err != nil {
		return &models.ValidationError{
			Field:   "id",
			Message: "ID must be a valid hexadecimal string",
		}
	}
	
	return nil
}

// ValidateSearchQuery validates search query parameters.
// This demonstrates search input validation and sanitization.
func ValidateSearchQuery(query string) error {
	trimmed := strings.TrimSpace(query)
	
	if trimmed == "" {
		return &models.ValidationError{
			Field:   "search",
			Message: "search query cannot be empty",
		}
	}
	
	if len(trimmed) < 2 {
		return &models.ValidationError{
			Field:   "search",
			Message: "search query must be at least 2 characters long",
		}
	}
	
	if len(trimmed) > 100 {
		return &models.ValidationError{
			Field:   "search",
			Message: "search query must not exceed 100 characters",
		}
	}
	
	return nil
}
