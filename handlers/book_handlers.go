// Package handlers contains HTTP request handlers for the book API.
// This demonstrates HTTP handling, routing, and REST API patterns in Go.
package handlers

import (
	"net/http"

	"github.com/gorilla/mux"

	"book-library-api/models"
	"book-library-api/utils"
)

// BookHandler contains the HTTP handlers for book operations.
// This demonstrates the handler pattern and dependency injection.
type BookHandler struct {
	service models.BookService
}

// NewBookHandler creates a new instance of the book handler.
// This is a constructor that demonstrates dependency injection.
func NewBookHandler(service models.BookService) *BookHandler {
	return &BookHandler{
		service: service,
	}
}

// CreateBook handles POST /books requests to create a new book.
// This demonstrates HTTP POST handling, JSON parsing, and response formatting.
func (h *BookHandler) CreateBook(w http.ResponseWriter, r *http.Request) {
	utils.LogRequest(r)
	
	// Parse the JSON request body
	var req models.BookCreateRequest
	if err := utils.ParseJSONBody(r, &req); err != nil {
		utils.WriteErrorResponse(w, &models.ValidationError{
			Field:   "body",
			Message: err.Error(),
		})
		return
	}
	
	// Create the book using the service
	book, err := h.service.CreateBook(&req)
	if err != nil {
		utils.WriteErrorResponse(w, err)
		return
	}
	
	// Return success response
	w.WriteHeader(http.StatusCreated)
	utils.WriteSuccessResponse(w, book, "Book created successfully")
}

// GetBook handles GET /books/{id} requests to retrieve a specific book.
// This demonstrates HTTP GET handling, URL parameter extraction, and error handling.
func (h *BookHandler) GetBook(w http.ResponseWriter, r *http.Request) {
	utils.LogRequest(r)
	
	// Extract the book ID from the URL path
	vars := mux.Vars(r)
	id := vars["id"]
	
	// Get the book using the service
	book, err := h.service.GetBook(id)
	if err != nil {
		utils.WriteErrorResponse(w, err)
		return
	}
	
	// Return success response
	utils.WriteSuccessResponse(w, book, "Book retrieved successfully")
}

// GetAllBooks handles GET /books requests to retrieve all books.
// This demonstrates handling collection endpoints and query parameters.
func (h *BookHandler) GetAllBooks(w http.ResponseWriter, r *http.Request) {
	utils.LogRequest(r)
	
	// Check if this is a search request
	query := utils.GetQueryParam(r, "search")
	if query != "" {
		h.SearchBooks(w, r)
		return
	}
	
	// Get all books using the service
	books, err := h.service.GetAllBooks()
	if err != nil {
		utils.WriteErrorResponse(w, err)
		return
	}
	
	// Return success response
	utils.WriteSuccessResponse(w, books, "Books retrieved successfully")
}

// UpdateBook handles PUT /books/{id} requests to update an existing book.
// This demonstrates HTTP PUT handling and partial updates.
func (h *BookHandler) UpdateBook(w http.ResponseWriter, r *http.Request) {
	utils.LogRequest(r)
	
	// Extract the book ID from the URL path
	vars := mux.Vars(r)
	id := vars["id"]
	
	// Parse the JSON request body
	var req models.BookUpdateRequest
	if err := utils.ParseJSONBody(r, &req); err != nil {
		utils.WriteErrorResponse(w, &models.ValidationError{
			Field:   "body",
			Message: err.Error(),
		})
		return
	}
	
	// Update the book using the service
	book, err := h.service.UpdateBook(id, &req)
	if err != nil {
		utils.WriteErrorResponse(w, err)
		return
	}
	
	// Return success response
	utils.WriteSuccessResponse(w, book, "Book updated successfully")
}

// DeleteBook handles DELETE /books/{id} requests to delete a book.
// This demonstrates HTTP DELETE handling and proper response codes.
func (h *BookHandler) DeleteBook(w http.ResponseWriter, r *http.Request) {
	utils.LogRequest(r)
	
	// Extract the book ID from the URL path
	vars := mux.Vars(r)
	id := vars["id"]
	
	// Delete the book using the service
	err := h.service.DeleteBook(id)
	if err != nil {
		utils.WriteErrorResponse(w, err)
		return
	}
	
	// Return success response with no content
	w.WriteHeader(http.StatusNoContent)
}

// SearchBooks handles GET /books?search=query requests to search for books.
// This demonstrates query parameter handling and search functionality.
func (h *BookHandler) SearchBooks(w http.ResponseWriter, r *http.Request) {
	utils.LogRequest(r)
	
	// Get the search query from URL parameters
	query := utils.GetQueryParam(r, "search")
	
	// Search for books using the service
	books, err := h.service.SearchBooks(query)
	if err != nil {
		utils.WriteErrorResponse(w, err)
		return
	}
	
	// Return success response
	utils.WriteSuccessResponse(w, books, "Search completed successfully")
}

// CheckoutBook handles POST /books/{id}/checkout requests to checkout a book.
// This demonstrates additional business operations beyond basic CRUD.
func (h *BookHandler) CheckoutBook(w http.ResponseWriter, r *http.Request) {
	utils.LogRequest(r)
	
	// Extract the book ID from the URL path
	vars := mux.Vars(r)
	id := vars["id"]
	
	// Checkout the book using the service
	book, err := h.service.CheckoutBook(id)
	if err != nil {
		utils.WriteErrorResponse(w, err)
		return
	}
	
	// Return success response
	utils.WriteSuccessResponse(w, book, "Book checked out successfully")
}

// ReturnBook handles POST /books/{id}/return requests to return a book.
// This demonstrates the complementary business operation to checkout.
func (h *BookHandler) ReturnBook(w http.ResponseWriter, r *http.Request) {
	utils.LogRequest(r)
	
	// Extract the book ID from the URL path
	vars := mux.Vars(r)
	id := vars["id"]
	
	// Return the book using the service
	book, err := h.service.ReturnBook(id)
	if err != nil {
		utils.WriteErrorResponse(w, err)
		return
	}
	
	// Return success response
	utils.WriteSuccessResponse(w, book, "Book returned successfully")
}
