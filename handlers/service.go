// Package handlers contains the business logic service layer.
// This demonstrates the separation of concerns between HTTP handling and business logic.
package handlers

import (
	"book-library-api/models"
	"book-library-api/utils"
)

// BookServiceImpl implements the BookService interface.
// This demonstrates dependency injection and business logic separation.
type BookServiceImpl struct {
	repo models.BookRepository
}

// NewBookService creates a new instance of the book service.
// This is a constructor that demonstrates dependency injection.
func NewBookService(repo models.BookRepository) models.BookService {
	return &BookServiceImpl{
		repo: repo,
	}
}

// CreateBook handles the business logic for creating a new book.
// This demonstrates validation, ID generation, and error handling.
func (s *BookServiceImpl) CreateBook(req *models.BookCreateRequest) (*models.Book, error) {
	// Validate the request
	if err := req.Validate(); err != nil {
		return nil, err
	}
	
	// Generate a unique ID for the book
	id, err := utils.GenerateID()
	if err != nil {
		return nil, err
	}
	
	// Convert request to book model
	book := req.ToBook(id)
	
	// Create the book in the repository
	return s.repo.Create(book)
}

// <PERSON><PERSON><PERSON> retrieves a book by ID.
// This demonstrates simple pass-through to the repository layer.
func (s *BookServiceImpl) GetBook(id string) (*models.Book, error) {
	if id == "" {
		return nil, &models.ValidationError{
			Field:   "id",
			Message: "book ID is required",
		}
	}
	
	return s.repo.GetByID(id)
}

// GetAllBooks retrieves all books.
// This demonstrates how business logic can be added to simple operations.
func (s *BookServiceImpl) GetAllBooks() ([]*models.Book, error) {
	return s.repo.GetAll()
}

// UpdateBook handles the business logic for updating an existing book.
// This demonstrates partial updates and business rule enforcement.
func (s *BookServiceImpl) UpdateBook(id string, req *models.BookUpdateRequest) (*models.Book, error) {
	if id == "" {
		return nil, &models.ValidationError{
			Field:   "id",
			Message: "book ID is required",
		}
	}
	
	// Get the existing book
	existingBook, err := s.repo.GetByID(id)
	if err != nil {
		return nil, err
	}
	
	// Apply the updates to a copy of the existing book
	updatedBook := *existingBook // Create a copy
	updatedBook.ApplyUpdate(req)
	
	// Validate the updated book if title or author changed
	if req.Title != nil || req.Author != nil {
		fields := make(map[string]string)
		if updatedBook.Title == "" {
			fields["title"] = updatedBook.Title
		}
		if updatedBook.Author == "" {
			fields["author"] = updatedBook.Author
		}
		if err := utils.ValidateRequiredFields(fields); err != nil {
			return nil, err
		}
	}
	
	// Update in repository
	return s.repo.Update(id, &updatedBook)
}

// DeleteBook handles the business logic for deleting a book.
// This demonstrates business rule validation before deletion.
func (s *BookServiceImpl) DeleteBook(id string) error {
	if id == "" {
		return &models.ValidationError{
			Field:   "id",
			Message: "book ID is required",
		}
	}
	
	// Check if book exists before attempting deletion
	_, err := s.repo.GetByID(id)
	if err != nil {
		return err
	}
	
	// Perform the deletion
	return s.repo.Delete(id)
}

// SearchBooks performs a search with business logic applied.
// This demonstrates search functionality and input sanitization.
func (s *BookServiceImpl) SearchBooks(query string) ([]*models.Book, error) {
	if query == "" {
		return nil, &models.ValidationError{
			Field:   "query",
			Message: "search query is required",
		}
	}
	
	// Sanitize the search query
	sanitizedQuery := utils.SanitizeString(query)
	
	return s.repo.Search(sanitizedQuery)
}

// CheckoutBook marks a book as unavailable.
// This demonstrates business logic for library operations.
func (s *BookServiceImpl) CheckoutBook(id string) (*models.Book, error) {
	if id == "" {
		return nil, &models.ValidationError{
			Field:   "id",
			Message: "book ID is required",
		}
	}
	
	// Get the existing book
	book, err := s.repo.GetByID(id)
	if err != nil {
		return nil, err
	}
	
	// Check if book is already checked out
	if !book.Available {
		return nil, &models.ConflictError{
			Resource: "Book",
			Field:    "availability",
			Value:    "unavailable",
		}
	}
	
	// Mark as unavailable
	available := false
	updateReq := &models.BookUpdateRequest{
		Available: &available,
	}

	return s.UpdateBook(id, updateReq)
}

// ReturnBook marks a book as available again.
// This demonstrates the opposite business logic operation.
func (s *BookServiceImpl) ReturnBook(id string) (*models.Book, error) {
	if id == "" {
		return nil, &models.ValidationError{
			Field:   "id",
			Message: "book ID is required",
		}
	}

	// Get the existing book
	book, err := s.repo.GetByID(id)
	if err != nil {
		return nil, err
	}

	// Check if book is already available
	if book.Available {
		return nil, &models.ConflictError{
			Resource: "Book",
			Field:    "availability",
			Value:    "available",
		}
	}

	// Mark as available
	available := true
	updateReq := &models.BookUpdateRequest{
		Available: &available,
	}

	return s.UpdateBook(id, updateReq)
}
