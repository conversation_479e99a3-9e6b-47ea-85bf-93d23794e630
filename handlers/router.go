// Package handlers contains the HTTP router setup and middleware.
// This demonstrates HTTP routing, middleware, and CORS handling in Go.
package handlers

import (
	"encoding/json"
	"net/http"

	"github.com/gorilla/mux"

	"book-library-api/models"
)

// SetupRouter creates and configures the HTTP router with all endpoints.
// This demonstrates REST API routing patterns and HTTP method mapping.
func SetupRouter(service models.BookService) *mux.Router {
	// Create a new router instance
	router := mux.NewRouter()
	
	// Create the book handler
	bookHandler := NewBookHandler(service)
	
	// Add middleware
	router.Use(corsMiddleware)
	router.Use(jsonMiddleware)
	
	// API version prefix
	api := router.PathPrefix("/api/v1").Subrouter()
	
	// Book endpoints - demonstrating RESTful API design
	books := api.PathPrefix("/books").Subrouter()
	
	// CRUD operations
	books.HandleFunc("", bookHandler.CreateBook).Methods("POST")           // Create
	books.HandleFunc("", bookHandler.GetAllBooks).Methods("GET")           // Read all / Search
	books.HandleFunc("/{id}", bookHandler.GetBook).Methods("GET")          // Read one
	books.HandleFunc("/{id}", bookHandler.UpdateBook).Methods("PUT")       // Update
	books.HandleFunc("/{id}", bookHandler.DeleteBook).Methods("DELETE")    // Delete
	
	// Additional business operations
	books.HandleFunc("/{id}/checkout", bookHandler.CheckoutBook).Methods("POST")
	books.HandleFunc("/{id}/return", bookHandler.ReturnBook).Methods("POST")
	
	// Health check endpoint
	api.HandleFunc("/health", healthCheckHandler).Methods("GET")
	
	// Root endpoint with API information
	router.HandleFunc("/", rootHandler).Methods("GET")
	
	return router
}

// corsMiddleware adds CORS headers to responses.
// This demonstrates middleware patterns and CORS handling.
func corsMiddleware(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// Set CORS headers
		w.Header().Set("Access-Control-Allow-Origin", "*")
		w.Header().Set("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		w.Header().Set("Access-Control-Allow-Headers", "Content-Type, Authorization")
		
		// Handle preflight requests
		if r.Method == "OPTIONS" {
			w.WriteHeader(http.StatusOK)
			return
		}
		
		// Continue to the next handler
		next.ServeHTTP(w, r)
	})
}

// jsonMiddleware sets the default content type for responses.
// This demonstrates simple middleware for response headers.
func jsonMiddleware(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// Set default content type
		w.Header().Set("Content-Type", "application/json")
		
		// Continue to the next handler
		next.ServeHTTP(w, r)
	})
}

// healthCheckHandler provides a simple health check endpoint.
// This demonstrates basic endpoint implementation and system monitoring.
func healthCheckHandler(w http.ResponseWriter, r *http.Request) {
	response := map[string]interface{}{
		"status":  "healthy",
		"service": "book-library-api",
		"version": "1.0.0",
	}
	
	w.WriteHeader(http.StatusOK)
	if err := writeJSON(w, response); err != nil {
		http.Error(w, "Internal server error", http.StatusInternalServerError)
	}
}

// rootHandler provides information about the API.
// This demonstrates API documentation endpoints.
func rootHandler(w http.ResponseWriter, r *http.Request) {
	response := map[string]interface{}{
		"name":        "Book Library API",
		"version":     "1.0.0",
		"description": "A simple REST API for managing a book library",
		"endpoints": map[string]interface{}{
			"health":      "/api/v1/health",
			"books":       "/api/v1/books",
			"create_book": "POST /api/v1/books",
			"get_books":   "GET /api/v1/books",
			"get_book":    "GET /api/v1/books/{id}",
			"update_book": "PUT /api/v1/books/{id}",
			"delete_book": "DELETE /api/v1/books/{id}",
			"search":      "GET /api/v1/books?search={query}",
			"checkout":    "POST /api/v1/books/{id}/checkout",
			"return":      "POST /api/v1/books/{id}/return",
		},
	}
	
	w.WriteHeader(http.StatusOK)
	if err := writeJSON(w, response); err != nil {
		http.Error(w, "Internal server error", http.StatusInternalServerError)
	}
}

// writeJSON is a helper function to write JSON responses.
// This demonstrates utility function patterns for common operations.
func writeJSON(w http.ResponseWriter, data interface{}) error {
	w.Header().Set("Content-Type", "application/json")
	return json.NewEncoder(w).Encode(data)
}
