// Package storage provides data storage implementations.
// This demonstrates <PERSON>'s interface implementation and concurrent programming concepts.
package storage

import (
	"strings"
	"sync"

	"book-library-api/models"
)

// MemoryBookRepository implements the BookRepository interface using in-memory storage.
// This demonstrates how Go interfaces work - any type that implements the required methods
// automatically satisfies the interface.
type MemoryBookRepository struct {
	// books stores the book data using a map for O(1) lookups by ID
	books map[string]*models.Book
	
	// mutex provides thread-safe access to the books map
	// This demonstrates <PERSON>'s approach to concurrent programming
	mutex sync.RWMutex
	
	// isbnIndex provides fast lookups by ISBN
	isbnIndex map[string]string // ISBN -> Book ID
}

// NewMemoryBookRepository creates a new instance of the in-memory repository.
// This is a constructor function, a common Go pattern.
func NewMemoryBookRepository() *MemoryBookRepository {
	return &MemoryBookRepository{
		books:     make(map[string]*models.Book),
		isbnIndex: make(map[string]string),
	}
}

// <PERSON>reate adds a new book to the in-memory storage.
// This demonstrates mutex usage for thread safety and error handling.
func (r *MemoryBookRepository) Create(book *models.Book) (*models.Book, error) {
	r.mutex.Lock()
	defer r.mutex.Unlock()
	
	// Check if a book with this ID already exists
	if _, exists := r.books[book.ID]; exists {
		return nil, &models.ConflictError{
			Resource: "Book",
			Field:    "ID",
			Value:    book.ID,
		}
	}
	
	// Check if ISBN is already in use (if provided)
	if book.ISBN != "" {
		if _, exists := r.isbnIndex[book.ISBN]; exists {
			return nil, &models.ConflictError{
				Resource: "Book",
				Field:    "ISBN",
				Value:    book.ISBN,
			}
		}
		r.isbnIndex[book.ISBN] = book.ID
	}
	
	// Store the book
	r.books[book.ID] = book
	
	return book, nil
}

// GetByID retrieves a book by its unique identifier.
// This demonstrates read locks for concurrent access and custom error types.
func (r *MemoryBookRepository) GetByID(id string) (*models.Book, error) {
	r.mutex.RLock()
	defer r.mutex.RUnlock()
	
	book, exists := r.books[id]
	if !exists {
		return nil, &models.NotFoundError{
			Resource: "Book",
			ID:       id,
		}
	}
	
	return book, nil
}

// GetAll retrieves all books from storage.
// This demonstrates slice operations and iteration patterns.
func (r *MemoryBookRepository) GetAll() ([]*models.Book, error) {
	r.mutex.RLock()
	defer r.mutex.RUnlock()
	
	// Create a slice to hold all books
	books := make([]*models.Book, 0, len(r.books))
	
	// Iterate through the map and collect all books
	for _, book := range r.books {
		books = append(books, book)
	}
	
	return books, nil
}

// Update modifies an existing book in storage.
// This demonstrates updating data structures and maintaining indexes.
func (r *MemoryBookRepository) Update(id string, updatedBook *models.Book) (*models.Book, error) {
	r.mutex.Lock()
	defer r.mutex.Unlock()
	
	// Check if the book exists
	existingBook, exists := r.books[id]
	if !exists {
		return nil, &models.NotFoundError{
			Resource: "Book",
			ID:       id,
		}
	}
	
	// Handle ISBN index updates
	if existingBook.ISBN != updatedBook.ISBN {
		// Remove old ISBN from index
		if existingBook.ISBN != "" {
			delete(r.isbnIndex, existingBook.ISBN)
		}
		
		// Check if new ISBN is already in use
		if updatedBook.ISBN != "" {
			if existingID, exists := r.isbnIndex[updatedBook.ISBN]; exists && existingID != id {
				return nil, &models.ConflictError{
					Resource: "Book",
					Field:    "ISBN",
					Value:    updatedBook.ISBN,
				}
			}
			r.isbnIndex[updatedBook.ISBN] = id
		}
	}
	
	// Update the book
	r.books[id] = updatedBook
	
	return updatedBook, nil
}

// Delete removes a book from storage by its ID.
// This demonstrates cleanup operations and maintaining data consistency.
func (r *MemoryBookRepository) Delete(id string) error {
	r.mutex.Lock()
	defer r.mutex.Unlock()
	
	// Check if the book exists
	book, exists := r.books[id]
	if !exists {
		return &models.NotFoundError{
			Resource: "Book",
			ID:       id,
		}
	}
	
	// Remove from ISBN index if it has an ISBN
	if book.ISBN != "" {
		delete(r.isbnIndex, book.ISBN)
	}
	
	// Remove from main storage
	delete(r.books, id)
	
	return nil
}

// GetByISBN retrieves a book by its ISBN.
// This demonstrates index usage for efficient lookups.
func (r *MemoryBookRepository) GetByISBN(isbn string) (*models.Book, error) {
	r.mutex.RLock()
	defer r.mutex.RUnlock()
	
	bookID, exists := r.isbnIndex[isbn]
	if !exists {
		return nil, &models.NotFoundError{
			Resource: "Book",
			ID:       isbn,
		}
	}
	
	return r.books[bookID], nil
}

// Search finds books that match the given query.
// This demonstrates string operations and filtering patterns.
func (r *MemoryBookRepository) Search(query string) ([]*models.Book, error) {
	r.mutex.RLock()
	defer r.mutex.RUnlock()
	
	var results []*models.Book
	queryLower := strings.ToLower(query)
	
	// Search through all books for matches in title, author, or genre
	for _, book := range r.books {
		if strings.Contains(strings.ToLower(book.Title), queryLower) ||
			strings.Contains(strings.ToLower(book.Author), queryLower) ||
			strings.Contains(strings.ToLower(book.Genre), queryLower) {
			results = append(results, book)
		}
	}
	
	return results, nil
}
