# 📚 Book Library API - Go Learning Project

A comprehensive REST API built with Go that demonstrates fundamental programming concepts through a practical book library management system. This project is designed as a learning resource for developers new to Go, showcasing best practices, clean architecture, and essential Go patterns.

## 🎯 Learning Objectives

By exploring this project, you'll learn:

- **Go Fundamentals**: Structs, interfaces, methods, and packages
- **HTTP Server Development**: Building REST APIs with proper routing
- **Error Handling**: Go's idiomatic error handling patterns
- **JSON Processing**: Marshaling and unmarshaling data
- **Concurrent Programming**: Thread-safe operations with mutexes
- **Clean Architecture**: Separation of concerns and dependency injection
- **Input Validation**: Comprehensive data validation techniques
- **Testing Patterns**: How to structure testable Go code

## 🛠️ Prerequisites

Before running this project, ensure you have:

- **Go 1.21 or later** installed on your system
- Basic understanding of REST APIs and HTTP methods
- A terminal or command prompt
- A text editor or IDE (VS Code, GoLand, etc.)

### Installing Go

If you don't have Go installed:

1. Visit [https://golang.org/dl/](https://golang.org/dl/)
2. Download the installer for your operating system
3. Follow the installation instructions
4. Verify installation: `go version`

## 🚀 Quick Start

### 1. Clone or Download the Project

```bash
# If using git
git clone <repository-url>
cd book-library-api

# Or download and extract the project files
```

### 2. Initialize Go Module (if needed)

```bash
go mod init book-library-api
go mod tidy
```

### 3. Install Dependencies

```bash
go get github.com/gorilla/mux
```

### 4. Run the Application

```bash
go run main.go
```

You should see output similar to:
```
🚀 Starting Book Library API...
📚 A learning-focused REST API built with Go

🌐 Server starting on port 8080
📖 API Documentation: http://localhost:8080/
🏥 Health Check: http://localhost:8080/api/v1/health
📚 Books Endpoint: http://localhost:8080/api/v1/books

Press Ctrl+C to stop the server
```

### 5. Test the API

Open your browser or use curl to test:

```bash
# Check if the server is running
curl http://localhost:8080/api/v1/health

# Get all books (includes sample data)
curl http://localhost:8080/api/v1/books
```

## 📋 API Endpoints

### Base URL
```
http://localhost:8080/api/v1
```

### Available Endpoints

| Method | Endpoint | Description |
|--------|----------|-------------|
| GET | `/` | API information and documentation |
| GET | `/api/v1/health` | Health check endpoint |
| GET | `/api/v1/books` | Get all books |
| GET | `/api/v1/books?search={query}` | Search books |
| GET | `/api/v1/books/{id}` | Get a specific book |
| POST | `/api/v1/books` | Create a new book |
| PUT | `/api/v1/books/{id}` | Update an existing book |
| DELETE | `/api/v1/books/{id}` | Delete a book |
| POST | `/api/v1/books/{id}/checkout` | Checkout a book |
| POST | `/api/v1/books/{id}/return` | Return a book |

## 📖 API Usage Examples

### Create a New Book

```bash
curl -X POST http://localhost:8080/api/v1/books \
  -H "Content-Type: application/json" \
  -d '{
    "title": "Learning Go",
    "author": "Jon Bodner",
    "isbn": "978-**********",
    "published_year": 2021,
    "genre": "technology",
    "description": "An idiomatic approach to real-world Go programming"
  }'
```

### Get All Books

```bash
curl http://localhost:8080/api/v1/books
```

### Search Books

```bash
curl "http://localhost:8080/api/v1/books?search=go"
```

### Get a Specific Book

```bash
curl http://localhost:8080/api/v1/books/{book-id}
```

### Update a Book

```bash
curl -X PUT http://localhost:8080/api/v1/books/{book-id} \
  -H "Content-Type: application/json" \
  -d '{
    "title": "Updated Title",
    "available": false
  }'
```

### Delete a Book

```bash
curl -X DELETE http://localhost:8080/api/v1/books/{book-id}
```

### Checkout a Book

```bash
curl -X POST http://localhost:8080/api/v1/books/{book-id}/checkout
```

### Return a Book

```bash
curl -X POST http://localhost:8080/api/v1/books/{book-id}/return
```

## 🏗️ Project Structure

```
book-library-api/
├── main.go                 # Application entry point
├── go.mod                  # Go module definition
├── go.sum                  # Dependency checksums
├── README.md              # This file
├── models/                # Data models and interfaces
│   ├── book.go           # Book struct and related types
│   ├── repository.go     # Repository and service interfaces
│   └── errors.go         # Custom error types
├── storage/               # Data storage implementations
│   └── memory.go         # In-memory storage implementation
├── handlers/              # HTTP handlers and business logic
│   ├── book_handlers.go  # HTTP request handlers
│   ├── service.go        # Business logic service
│   └── router.go         # HTTP routing setup
└── utils/                 # Utility functions
    ├── helpers.go        # General helper functions
    └── validation.go     # Input validation functions
```

## 🧠 Key Go Concepts Demonstrated

### 1. Structs and JSON Tags
```go
type Book struct {
    ID     string `json:"id"`
    Title  string `json:"title" validate:"required"`
    Author string `json:"author" validate:"required"`
    // ...
}
```

### 2. Interfaces and Dependency Injection
```go
type BookRepository interface {
    Create(book *Book) (*Book, error)
    GetByID(id string) (*Book, error)
    // ...
}
```

### 3. Error Handling
```go
func (s *BookService) CreateBook(req *BookCreateRequest) (*Book, error) {
    if err := req.Validate(); err != nil {
        return nil, err
    }
    // ...
}
```

### 4. HTTP Handlers
```go
func (h *BookHandler) CreateBook(w http.ResponseWriter, r *http.Request) {
    var req models.BookCreateRequest
    if err := utils.ParseJSONBody(r, &req); err != nil {
        utils.WriteErrorResponse(w, err)
        return
    }
    // ...
}
```

### 5. Concurrent Programming
```go
type MemoryBookRepository struct {
    books map[string]*Book
    mutex sync.RWMutex  // Thread-safe access
}
```

## 🔧 Configuration

The application can be configured using environment variables:

- `PORT`: Server port (default: 8080)

Example:
```bash
PORT=3000 go run main.go
```

## 🧪 Testing the API

### Using curl (Command Line)

All the examples above use curl. Make sure to replace `{book-id}` with actual book IDs from your API responses.

### Using a REST Client

You can also use tools like:
- Postman
- Insomnia
- VS Code REST Client extension

### Sample Data

The application starts with sample books including:
- The Go Programming Language
- Clean Code
- The Pragmatic Programmer
- Design Patterns
- Effective Go

## 🚨 Error Handling

The API returns standardized error responses:

```json
{
  "success": false,
  "error": "Book with ID 'invalid-id' not found"
}
```

Common HTTP status codes:
- `200`: Success
- `201`: Created
- `204`: No Content (for deletions)
- `400`: Bad Request (validation errors)
- `404`: Not Found
- `409`: Conflict (duplicate resources)
- `500`: Internal Server Error

## 📚 Learning Path

### Beginner Level
1. Understand the project structure
2. Run the application and test basic endpoints
3. Examine the `models/book.go` file to understand structs
4. Look at `handlers/book_handlers.go` for HTTP handling

### Intermediate Level
1. Study the interface definitions in `models/repository.go`
2. Understand dependency injection in `main.go`
3. Examine error handling patterns throughout the code
4. Learn about concurrent programming in `storage/memory.go`

### Advanced Level
1. Implement additional features (user management, book categories)
2. Add database integration (PostgreSQL, MongoDB)
3. Implement authentication and authorization
4. Add comprehensive unit and integration tests
5. Deploy to cloud platforms (AWS, GCP, Azure)

## 🔄 Next Steps for Further Learning

1. **Add Database Integration**
   - Replace in-memory storage with PostgreSQL or MongoDB
   - Learn about database/sql package and ORMs like GORM

2. **Implement Authentication**
   - Add JWT-based authentication
   - Implement user roles and permissions

3. **Add Testing**
   - Write unit tests for all components
   - Add integration tests for API endpoints
   - Learn about Go's testing package

4. **Improve Logging**
   - Add structured logging with logrus or zap
   - Implement request/response logging middleware

5. **Add Configuration Management**
   - Use viper for configuration management
   - Support multiple environments (dev, staging, prod)

6. **Containerization**
   - Create Dockerfile for the application
   - Learn about Docker and container orchestration

7. **API Documentation**
   - Add Swagger/OpenAPI documentation
   - Generate interactive API documentation

## 🤝 Contributing

This is a learning project! Feel free to:
- Add new features
- Improve error handling
- Add tests
- Enhance documentation
- Fix bugs

## 📄 License

This project is created for educational purposes. Feel free to use it for learning and teaching Go programming.

---

## 🧪 Complete Testing Guide

### Step-by-Step Testing Walkthrough

1. **Start the server**:
   ```bash
   go run main.go
   ```

2. **Test health endpoint**:
   ```bash
   curl http://localhost:8080/api/v1/health
   ```

3. **View sample data**:
   ```bash
   curl http://localhost:8080/api/v1/books | jq
   ```

4. **Create a new book**:
   ```bash
   curl -X POST http://localhost:8080/api/v1/books \
     -H "Content-Type: application/json" \
     -d '{
       "title": "Test Book",
       "author": "Test Author",
       "genre": "fiction"
     }' | jq
   ```

5. **Search for books**:
   ```bash
   curl "http://localhost:8080/api/v1/books?search=go" | jq
   ```

6. **Update a book** (use ID from previous responses):
   ```bash
   curl -X PUT http://localhost:8080/api/v1/books/YOUR_BOOK_ID \
     -H "Content-Type: application/json" \
     -d '{"title": "Updated Title"}' | jq
   ```

7. **Test checkout/return**:
   ```bash
   # Checkout
   curl -X POST http://localhost:8080/api/v1/books/YOUR_BOOK_ID/checkout | jq

   # Return
   curl -X POST http://localhost:8080/api/v1/books/YOUR_BOOK_ID/return | jq
   ```

### Expected Response Formats

**Success Response**:
```json
{
  "success": true,
  "data": {
    "id": "a1b2c3d4e5f6g7h8",
    "title": "The Go Programming Language",
    "author": "Alan Donovan and Brian Kernighan",
    "available": true,
    "created_at": "2024-01-01T12:00:00Z",
    "updated_at": "2024-01-01T12:00:00Z"
  },
  "message": "Book retrieved successfully"
}
```

**Error Response**:
```json
{
  "success": false,
  "error": "Book with ID 'invalid-id' not found"
}
```

---

**Happy Learning! 🎉**

Remember: The best way to learn programming is by doing. Experiment with the code, break things, fix them, and most importantly, have fun while learning Go!
